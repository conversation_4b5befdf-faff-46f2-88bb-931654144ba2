{"sources": [{"file": "D:/vscode/projects-lvgl/sample_project/build/CMakeFiles/bootloader"}, {"file": "D:/vscode/projects-lvgl/sample_project/build/CMakeFiles/bootloader.rule"}, {"file": "D:/vscode/projects-lvgl/sample_project/build/CMakeFiles/bootloader-complete.rule"}, {"file": "D:/vscode/projects-lvgl/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "D:/vscode/projects-lvgl/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "D:/vscode/projects-lvgl/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "D:/vscode/projects-lvgl/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "D:/vscode/projects-lvgl/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "D:/vscode/projects-lvgl/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "D:/vscode/projects-lvgl/sample_project/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}