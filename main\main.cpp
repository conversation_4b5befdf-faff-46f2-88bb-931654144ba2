#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_log.h"
#include "driver/uart.h"
#include "Adafruit_Fingerprint.h"

static const char* TAG = "MAIN";

// UART configuration for fingerprint sensor
#define FINGERPRINT_UART_NUM UART_NUM_2
#define FINGERPRINT_TXD_PIN 17
#define FINGERPRINT_RXD_PIN 16

// UART configuration for console output
#define CONSOLE_UART_NUM UART_NUM_0

// Create serial interface and fingerprint sensor objects
ESPSerial mySerial(FINGERPRINT_UART_NUM);
ESPSerial Serial(CONSOLE_UART_NUM);  // Console serial for debugging
Adafruit_Fingerprint finger(&mySerial);

// Function prototypes
uint8_t getFingerprintID();
int getFingerprintIDez();
void setup();
void loop();

void setup()
{
  // Initialize console UART (already initialized by ESP-IDF, but we set up our wrapper)
  Serial.print("התחלתי את setup");
  Serial.print("\r\n");

  delay(100);
  Serial.print("\n\nAdafruit finger detect test\r\n");
  
  // Initialize fingerprint sensor UART
  mySerial.begin(57600, 0, FINGERPRINT_RXD_PIN, FINGERPRINT_TXD_PIN);
  
  // Initialize fingerprint sensor
  finger.begin(57600);
  delay(5);
  
  if (finger.verifyPassword()) {
    Serial.print("Found fingerprint sensor!\r\n");
  } else {
    Serial.print("Did not find fingerprint sensor :(\r\n");
    while (1) { delay(1); }
  }

  Serial.print("Reading sensor parameters\r\n");
  finger.getParameters();
  Serial.print("Status: 0x"); Serial.print(finger.status_reg, 16); Serial.print("\r\n");
  Serial.print("Sys ID: 0x"); Serial.print(finger.system_id, 16); Serial.print("\r\n");
  Serial.print("Capacity: "); Serial.print((int)finger.capacity); Serial.print("\r\n");
  Serial.print("Security level: "); Serial.print((int)finger.security_level); Serial.print("\r\n");
  Serial.print("Device address: "); Serial.print(finger.device_addr, 16); Serial.print("\r\n");
  Serial.print("Packet len: "); Serial.print((int)finger.packet_len); Serial.print("\r\n");
  Serial.print("Baud rate: "); Serial.print(finger.baud_rate); Serial.print("\r\n");

  finger.getTemplateCount();

  if (finger.templateCount == 0) {
    Serial.print("Sensor doesn't contain any fingerprint data. Please run the 'enroll' example.\r\n");
  }
  else {
    Serial.print("Waiting for valid finger...\r\n");
    Serial.print("Sensor contains "); Serial.print((int)finger.templateCount); Serial.print(" templates\r\n");
  }
}

void loop()                     // run over and over again
{
  getFingerprintID();
  delay(50);            //don't need to run this at full speed.
}

uint8_t getFingerprintID() {
  uint8_t p = finger.getImage();
  switch (p) {
    case FINGERPRINT_OK:
      Serial.print("Image taken\r\n");
      break;
    case FINGERPRINT_NOFINGER:
      Serial.print("No finger detected\r\n");
      return p;
    case FINGERPRINT_PACKETRECIEVEERR:
      Serial.print("Communication error\r\n");
      return p;
    case FINGERPRINT_IMAGEFAIL:
      Serial.print("Imaging error\r\n");
      return p;
    default:
      Serial.print("Unknown error\r\n");
      return p;
  }

  // OK success!

  p = finger.image2Tz();
  switch (p) {
    case FINGERPRINT_OK:
      Serial.print("Image converted\r\n");
      break;
    case FINGERPRINT_IMAGEMESS:
      Serial.print("Image too messy\r\n");
      return p;
    case FINGERPRINT_PACKETRECIEVEERR:
      Serial.print("Communication error\r\n");
      return p;
    case FINGERPRINT_FEATUREFAIL:
      Serial.print("Could not find fingerprint features\r\n");
      return p;
    case FINGERPRINT_INVALIDIMAGE:
      Serial.print("Could not find fingerprint features\r\n");
      return p;
    default:
      Serial.print("Unknown error\r\n");
      return p;
  }

  // OK converted!
  p = finger.fingerSearch();
  if (p == FINGERPRINT_OK) {
    Serial.print("Found a print match!\r\n");
  } else if (p == FINGERPRINT_PACKETRECIEVEERR) {
    Serial.print("Communication error\r\n");
    return p;
  } else if (p == FINGERPRINT_NOTFOUND) {
    Serial.print("Did not find a match\r\n");
    return p;
  } else {
    Serial.print("Unknown error\r\n");
    return p;
  }

  // found a match!
  Serial.print("Found ID #"); Serial.print((int)finger.fingerID);
  Serial.print(" with confidence of "); Serial.print((int)finger.confidence); Serial.print("\r\n");

  return finger.fingerID;
}

// returns -1 if failed, otherwise returns ID #
int getFingerprintIDez() {
  uint8_t p = finger.getImage();
  if (p != FINGERPRINT_OK)  return -1;

  p = finger.image2Tz();
  if (p != FINGERPRINT_OK)  return -1;

  p = finger.fingerFastSearch();
  if (p != FINGERPRINT_OK)  return -1;

  // found a match!
  Serial.print("Found ID #"); Serial.print((int)finger.fingerID);
  Serial.print(" with confidence of "); Serial.print((int)finger.confidence); Serial.print("\r\n");
  return finger.fingerID;
}

// ESP-IDF main function
extern "C" void app_main(void)
{
    ESP_LOGI(TAG, "Starting fingerprint sensor application");

    // Initialize console serial (UART0 is already initialized by ESP-IDF)
    Serial.begin(115200, 0, -1, -1);  // Console doesn't need pin setup

    // Run setup once
    setup();

    // Run loop continuously
    while(1) {
        loop();
    }
}
