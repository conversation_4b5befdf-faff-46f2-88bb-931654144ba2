/*!
 * @file Adafruit_Fingerprint.cpp
 * ESP-IDF port of the Adafruit Fingerprint Sensor Library
 */

#include "Adafruit_Fingerprint.h"

static const char* TAG = "FINGERPRINT";

// Delay function for ESP-IDF
void delay(uint32_t ms) {
    vTaskDelay(pdMS_TO_TICKS(ms));
}

/**************************************************************************/
/*!
    @brief   ESPSerial constructor
    @param   uart_num UART port number
*/
/**************************************************************************/
ESPSerial::ESPSerial(uart_port_t uart_num) {
    uart_port = uart_num;
    initialized = false;
}

/**************************************************************************/
/*!
    @brief   Initialize UART with specified parameters
    @param   baud Baud rate
    @param   config UART configuration (not used, kept for compatibility)
    @param   rxPin RX pin number
    @param   txPin TX pin number
*/
/**************************************************************************/
void ESPSerial::begin(uint32_t baud, uint32_t config, int8_t rxPin, int8_t txPin) {
    // UART0 is already initialized by ESP-IDF for console
    if (uart_port == UART_NUM_0) {
        initialized = true;
        return;
    }

    uart_config_t uart_config = {
        .baud_rate = (int)baud,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .rx_flow_ctrl_thresh = 122,
    };

    ESP_ERROR_CHECK(uart_param_config(uart_port, &uart_config));
    ESP_ERROR_CHECK(uart_set_pin(uart_port, txPin, rxPin, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));
    ESP_ERROR_CHECK(uart_driver_install(uart_port, 1024, 1024, 0, NULL, 0));

    initialized = true;
}

/**************************************************************************/
/*!
    @brief   Write a single byte
    @param   data Byte to write
*/
/**************************************************************************/
void ESPSerial::write(uint8_t data) {
    if (uart_port == UART_NUM_0) {
        // Use printf for console output
        printf("%c", data);
    } else if (initialized) {
        uart_write_bytes(uart_port, &data, 1);
    }
}

/**************************************************************************/
/*!
    @brief   Check if data is available to read
    @returns Number of bytes available
*/
/**************************************************************************/
int ESPSerial::available() {
    if (!initialized) return 0;
    
    size_t available_bytes;
    uart_get_buffered_data_len(uart_port, &available_bytes);
    return (int)available_bytes;
}

/**************************************************************************/
/*!
    @brief   Read a single byte
    @returns Byte read from UART
*/
/**************************************************************************/
uint8_t ESPSerial::read() {
    if (!initialized) return 0;
    
    uint8_t data;
    int len = uart_read_bytes(uart_port, &data, 1, pdMS_TO_TICKS(10));
    return (len > 0) ? data : 0;
}

/**************************************************************************/
/*!
    @brief   Print a string
    @param   str String to print
*/
/**************************************************************************/
void ESPSerial::print(const char* str) {
    if (uart_port == UART_NUM_0) {
        // Use printf for console output
        printf("%s", str);
    } else if (initialized && str) {
        uart_write_bytes(uart_port, str, strlen(str));
    }
}

/**************************************************************************/
/*!
    @brief   Print a string with newline
    @param   str String to print
*/
/**************************************************************************/
void ESPSerial::println(const char* str) {
    print(str);
    print("\r\n");
}

/**************************************************************************/
/*!
    @brief   Print an integer
    @param   value Integer to print
    @param   base Number base (10 for decimal, 16 for hex)
*/
/**************************************************************************/
void ESPSerial::print(int value, int base) {
    char buffer[32];
    if (base == 16) {
        snprintf(buffer, sizeof(buffer), "%X", value);
    } else {
        snprintf(buffer, sizeof(buffer), "%d", value);
    }
    print(buffer);
}

/**************************************************************************/
/*!
    @brief   Print an integer with newline
    @param   value Integer to print
    @param   base Number base (10 for decimal, 16 for hex)
*/
/**************************************************************************/
void ESPSerial::println(int value, int base) {
    print(value, base);
    print("\r\n");
}

/**************************************************************************/
/*!
    @brief   Print an unsigned integer
    @param   value Integer to print
    @param   base Number base (10 for decimal, 16 for hex)
*/
/**************************************************************************/
void ESPSerial::print(uint32_t value, int base) {
    char buffer[32];
    if (base == 16) {
        snprintf(buffer, sizeof(buffer), "%lX", (unsigned long)value);
    } else {
        snprintf(buffer, sizeof(buffer), "%lu", (unsigned long)value);
    }
    print(buffer);
}

/**************************************************************************/
/*!
    @brief   Print an unsigned integer with newline
    @param   value Integer to print
    @param   base Number base (10 for decimal, 16 for hex)
*/
/**************************************************************************/
void ESPSerial::println(uint32_t value, int base) {
    print(value, base);
    print("\r\n");
}

/**************************************************************************/
/*!
    @brief   Gets the command packet
*/
/**************************************************************************/
#define GET_CMD_PACKET(...) \
  uint8_t data[] = {__VA_ARGS__}; \
  Adafruit_Fingerprint_Packet packet(FINGERPRINT_COMMANDPACKET, sizeof(data), \
                                     data); \
  writeStructuredPacket(packet); \
  if (getStructuredPacket(&packet) != FINGERPRINT_OK) \
    return FINGERPRINT_PACKETRECIEVEERR; \
  if (packet.type != FINGERPRINT_ACKPACKET) \
    return FINGERPRINT_PACKETRECIEVEERR;

/**************************************************************************/
/*!
    @brief   Sends the command packet
*/
/**************************************************************************/
#define SEND_CMD_PACKET(...) \
  GET_CMD_PACKET(__VA_ARGS__); \
  return packet.data[0];

/**************************************************************************/
/*!
    @brief   Instantiates sensor with ESP Serial
    @param   serial Pointer to ESPSerial object
    @param   password 32-bit integer password (default is 0)
*/
/**************************************************************************/
Adafruit_Fingerprint::Adafruit_Fingerprint(ESPSerial *serial, uint32_t password) {
  thePassword = password;
  theAddress = 0xFFFFFFFF;
  mySerial = serial;
}

/**************************************************************************/
/*!
    @brief   Initializes serial interface and baud rate
    @param   baudrate Sensor's UART baud rate (usually 57600, 9600 or 115200)
*/
/**************************************************************************/
void Adafruit_Fingerprint::begin(uint32_t baudrate) {
  delay(1000); // one second delay to let the sensor 'boot up'
  // Note: ESPSerial should already be initialized before calling this
}

/**************************************************************************/
/*!
    @brief   Verifies the sensors' access password
    @returns True if password is correct
*/
/**************************************************************************/
boolean Adafruit_Fingerprint::verifyPassword(void) {
  return checkPassword() == FINGERPRINT_OK;
}

uint8_t Adafruit_Fingerprint::checkPassword(void) {
  GET_CMD_PACKET(FINGERPRINT_VERIFYPASSWORD, (uint8_t)(thePassword >> 24),
                 (uint8_t)(thePassword >> 16), (uint8_t)(thePassword >> 8),
                 (uint8_t)(thePassword & 0xFF));
  if (packet.data[0] == FINGERPRINT_OK)
    return FINGERPRINT_OK;
  else
    return FINGERPRINT_PACKETRECIEVEERR;
}

/**************************************************************************/
/*!
    @brief   Get the sensors parameters
    @returns FINGERPRINT_OK on success
*/
/**************************************************************************/
uint8_t Adafruit_Fingerprint::getParameters(void) {
  GET_CMD_PACKET(FINGERPRINT_READSYSPARAM);

  status_reg = ((uint16_t)packet.data[1] << 8) | packet.data[2];
  system_id = ((uint16_t)packet.data[3] << 8) | packet.data[4];
  capacity = ((uint16_t)packet.data[5] << 8) | packet.data[6];
  security_level = ((uint16_t)packet.data[7] << 8) | packet.data[8];
  device_addr = ((uint32_t)packet.data[9] << 24) |
                ((uint32_t)packet.data[10] << 16) |
                ((uint32_t)packet.data[11] << 8) | (uint32_t)packet.data[12];
  packet_len = ((uint16_t)packet.data[13] << 8) | packet.data[14];
  if (packet_len == 0) {
    packet_len = 32;
  } else if (packet_len == 1) {
    packet_len = 64;
  } else if (packet_len == 2) {
    packet_len = 128;
  } else if (packet_len == 3) {
    packet_len = 256;
  }
  baud_rate = (((uint16_t)packet.data[15] << 8) | packet.data[16]) * 9600;

  return packet.data[0];
}

/**************************************************************************/
/*!
    @brief   Ask the sensor to take an image of the finger
    @returns FINGERPRINT_OK on success
*/
/**************************************************************************/
uint8_t Adafruit_Fingerprint::getImage(void) {
  SEND_CMD_PACKET(FINGERPRINT_GETIMAGE);
}

/**************************************************************************/
/*!
    @brief   Ask the sensor to convert image to feature template
    @param   slot Location to place feature template
    @returns FINGERPRINT_OK on success
*/
/**************************************************************************/
uint8_t Adafruit_Fingerprint::image2Tz(uint8_t slot) {
  SEND_CMD_PACKET(FINGERPRINT_IMAGE2TZ, slot);
}

/**************************************************************************/
/*!
    @brief   Ask the sensor to search for fingerprint
    @param   slot The slot to use for the print search
    @returns FINGERPRINT_OK on fingerprint match success
*/
/**************************************************************************/
uint8_t Adafruit_Fingerprint::fingerSearch(uint8_t slot) {
  GET_CMD_PACKET(FINGERPRINT_SEARCH, slot, 0x00, 0x00, (uint8_t)(capacity >> 8),
                 (uint8_t)(capacity & 0xFF));

  fingerID = 0xFFFF;
  confidence = 0xFFFF;

  fingerID = packet.data[1];
  fingerID <<= 8;
  fingerID |= packet.data[2];

  confidence = packet.data[3];
  confidence <<= 8;
  confidence |= packet.data[4];

  return packet.data[0];
}

/**************************************************************************/
/*!
    @brief   Ask the sensor for fast search
    @returns FINGERPRINT_OK on fingerprint match success
*/
/**************************************************************************/
uint8_t Adafruit_Fingerprint::fingerFastSearch(void) {
  GET_CMD_PACKET(FINGERPRINT_HISPEEDSEARCH, 0x01, 0x00, 0x00, 0x00, 0xA3);

  fingerID = 0xFFFF;
  confidence = 0xFFFF;

  fingerID = packet.data[1];
  fingerID <<= 8;
  fingerID |= packet.data[2];

  confidence = packet.data[3];
  confidence <<= 8;
  confidence |= packet.data[4];

  return packet.data[0];
}

/**************************************************************************/
/*!
    @brief   Ask the sensor for the number of templates stored
    @returns FINGERPRINT_OK on success
*/
/**************************************************************************/
uint8_t Adafruit_Fingerprint::getTemplateCount(void) {
  GET_CMD_PACKET(FINGERPRINT_TEMPLATECOUNT);

  templateCount = packet.data[1];
  templateCount <<= 8;
  templateCount |= packet.data[2];

  return packet.data[0];
}

/**************************************************************************/
/*!
    @brief   Helper function to process a packet and send it over UART
    @param   packet A structure containing the bytes to transmit
*/
/**************************************************************************/
void Adafruit_Fingerprint::writeStructuredPacket(
    const Adafruit_Fingerprint_Packet &packet) {
  mySerial->write((uint8_t)(packet.start_code >> 8));
  mySerial->write((uint8_t)(packet.start_code & 0xFF));
  mySerial->write(packet.address[0]);
  mySerial->write(packet.address[1]);
  mySerial->write(packet.address[2]);
  mySerial->write(packet.address[3]);
  mySerial->write(packet.type);

  uint16_t wire_length = packet.length + 2;
  mySerial->write((uint8_t)(wire_length >> 8));
  mySerial->write((uint8_t)(wire_length & 0xFF));

  uint16_t sum = ((wire_length) >> 8) + ((wire_length) & 0xFF) + packet.type;
  for (uint8_t i = 0; i < packet.length; i++) {
    mySerial->write(packet.data[i]);
    sum += packet.data[i];
  }

  mySerial->write((uint8_t)(sum >> 8));
  mySerial->write((uint8_t)(sum & 0xFF));
}

/**************************************************************************/
/*!
    @brief   Helper function to receive data over UART and process it
    @param   packet A structure containing the bytes received
    @param   timeout How many milliseconds we're willing to wait
    @returns FINGERPRINT_OK on success
*/
/**************************************************************************/
uint8_t Adafruit_Fingerprint::getStructuredPacket(
    Adafruit_Fingerprint_Packet *packet, uint16_t timeout) {
  uint8_t byte;
  uint16_t idx = 0, timer = 0;

  while (true) {
    while (!mySerial->available()) {
      delay(1);
      timer++;
      if (timer >= timeout) {
        return FINGERPRINT_TIMEOUT;
      }
    }

    byte = mySerial->read();

    switch (idx) {
    case 0:
      if (byte != (FINGERPRINT_STARTCODE >> 8))
        continue;
      packet->start_code = (uint16_t)byte << 8;
      break;
    case 1:
      packet->start_code |= byte;
      if (packet->start_code != FINGERPRINT_STARTCODE)
        return FINGERPRINT_BADPACKET;
      break;
    case 2:
    case 3:
    case 4:
    case 5:
      packet->address[idx - 2] = byte;
      break;
    case 6:
      packet->type = byte;
      break;
    case 7:
      packet->length = (uint16_t)byte << 8;
      break;
    case 8:
      packet->length |= byte;
      break;
    default:
      packet->data[idx - 9] = byte;
      if ((idx - 8) == packet->length) {
        return FINGERPRINT_OK;
      }
      break;
    }
    idx++;
    if ((idx + 9) >= sizeof(packet->data)) {
      return FINGERPRINT_BADPACKET;
    }
  }
  return FINGERPRINT_BADPACKET;
}
